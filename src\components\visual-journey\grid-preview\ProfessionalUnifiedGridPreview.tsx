import React, { useRef, useEffect, useState } from 'react';
import { motion } from 'framer-motion';
import { gsap } from 'gsap';
import { useGameStore } from '../../../store';
import { useSlotLayout } from '../../../hooks/useSlotLayout';
import SlotGameUI from '../slot-animation/SlotGameUI';
import MobileLandscapeUI from '../slot-animation/MobileLandscapeUI';
import MobilePortraitUI from '../slot-animation/MobilePortraitUI';
import { useStoredSymbols } from '../../../utils/symbolStorage';

/**
 * Professional Unified Grid Preview
 * 
 * EXACT copy of UnifiedGridPreview but with professional GSAP animations
 * Maintains all existing layout, responsive system, device mockups, and UI
 */

// Copy exact props interface from UnifiedGridPreview
interface ProfessionalUnifiedGridPreviewProps {
  /** Reels count (width) */
  reels: number;
  /** Rows count (height) */
  rows: number;
  /** Whether to animate grid cells */
  animate?: boolean;
  /** Pay mechanism (betlines, ways, cluster) */
  payMechanism?: string;
  /** Orientation of the grid (landscape or portrait) */
  orientation: 'landscape' | 'portrait';
  /** Additional CSS classes */
  className?: string;
  /** Enable debug label */
  showDebugLabel?: boolean;
  /** Optional id for the grid - helpful for event listeners */
  id?: string;
  /** Whether to automatically scale the grid to fit its container */
  scaleToFit?: boolean;
  /** Scale modifier for fine-tuning grid size (0.9 = 90% of default size) */
  gridScaleModifier?: number;
  /** Vertical offset for centering adjustment (positive = move down, negative = move up) */
  verticalOffset?: number;
  /** SVG string for custom spin button */
  spinButtonSvg?: string;
  /** Image URL for custom spin button */
  spinButtonImageUrl?: string;
  /** Handler for spin button click */
  onSpin?: () => void;
  /** Current balance value */
  balance?: number;
  /** Current bet value */
  bet?: number;
  /** Current win value */
  win?: number;
  /** Whether to show the unified UI components (optional) */
  showUnifiedUI?: boolean;
  /** Path to the frame image */
  framePath?: string | null;
  /** Frame position {x, y} */
  framePosition?: { x: number; y: number };
  /** Frame scale (percentage) */
  frameScale?: number;
  /** Frame stretch {x, y} (percentage) */
  frameStretch?: { x: number; y: number };
  /** Path to the background image */
  backgroundPath?: string | null;
  /** Whether to show cell backgrounds */
  showCellBackgrounds?: boolean;
}

const ProfessionalUnifiedGridPreview: React.FC<ProfessionalUnifiedGridPreviewProps> = ({
  reels,
  rows,
  animate = true,
  payMechanism = 'betlines',
  orientation = 'landscape',
  className = '',
  showDebugLabel = true,
  id = 'slot-grid',
  scaleToFit = false,
  gridScaleModifier = 0.94,
  verticalOffset = 0,
  spinButtonSvg,
  spinButtonImageUrl,
  onSpin = () => console.log('Spin clicked'),
  balance = 1000,
  bet = 1.00,
  win = 0.00,
  showUnifiedUI = true,
  framePath = null,
  framePosition = { x: 0, y: 0 },
  frameScale = 100,
  frameStretch = { x: 100, y: 100 },
  backgroundPath = null,
  showCellBackgrounds = false
}) => {
  const { theme, config } = useGameStore(state => ({
    theme: state.theme,
    config: state.config
  }));
  
  // Copy exact state from UnifiedGridPreview
  const [symbolImages, setSymbolImages] = useState<string[]>([]);
  const [symbolsLoading, setSymbolsLoading] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const symbolStore = useStoredSymbols();
  
  // Professional spinning state with GSAP
  const [isSpinning, setIsSpinning] = useState(false);
  const [spinResult, setSpinResult] = useState<string[][]>([]);
  const [stoppedReels, setStoppedReels] = useState<boolean[]>([]);
  
  // Refs for GSAP animations
  const gridRef = useRef<HTMLDivElement>(null);
  const gridContentRef = useRef<HTMLDivElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const reelRefs = useRef<HTMLDivElement[]>([]);
  
  const requestInProgressRef = useRef(false);
  const prevSymbolsRef = useRef<string[]>([]);
  
  // Copy exact state management from UnifiedGridPreview
  const [currentOrientation, setCurrentOrientation] = useState<'landscape' | 'portrait'>(orientation);
  const [isMobile, setIsMobile] = useState(false);
  const [isLandscape, setIsLandscape] = useState(false);
  
  // Use exact same layout calculations
  const { 
    isSmallGrid, 
    isMediumGrid,
    isLargeGrid,
    gapSize, 
    symbolSize, 
    fontSize, 
    gridPadding,
    containerAspectRatio,
    debugLabel,
    gridWidthPct,
    gridHeightPct,
    gridScale,
    virtualContainerWidth,
    virtualContainerHeight
  } = useSlotLayout(reels, rows, currentOrientation);
  
  // Copy exact mobile detection from UnifiedGridPreview
  useEffect(() => {
    const detectMobile = () => {
      const userAgent = navigator.userAgent || navigator.vendor || (window as any).opera;
      const mobileRegex = /android|webos|iphone|ipad|ipod|blackberry|iemobile|opera mini/i;
      const isMobileByUA = mobileRegex.test(userAgent.toLowerCase());
      
      const isMobileBySize = window.innerWidth < 768;
      const isMobileByTouch = ('ontouchstart' in window) || 
                            (navigator.maxTouchPoints > 0) || 
                            ((navigator as any).msMaxTouchPoints > 0);
      
      const mobileDetectionCount = [isMobileByUA, isMobileBySize, isMobileByTouch].filter(Boolean).length;
      const isMobileDevice = mobileDetectionCount >= 2 || isMobileByUA;
      
      const isLandscapeMode = (window.innerWidth > window.innerHeight) && 
                             window.matchMedia("(orientation: landscape)").matches;
      
      const urlParams = new URLSearchParams(window.location.search);
      const forceMobile = urlParams.get('forceMobile') === 'true';
      const forceLandscape = urlParams.get('forceLandscape') === 'true';
      
      setIsMobile(forceMobile || isMobileDevice);
      setIsLandscape(forceLandscape || isLandscapeMode);
    };
    
    detectMobile();
    
    const updateDetection = () => {
      const isLandscapeMode = window.innerWidth > window.innerHeight;
      setIsLandscape(isLandscapeMode);
      detectMobile();
    };
    
    window.addEventListener('resize', updateDetection);
    window.addEventListener('orientationchange', updateDetection);
    
    return () => {
      window.removeEventListener('resize', updateDetection);
      window.removeEventListener('orientationchange', updateDetection);
    };
  }, []);
  
  // Copy all symbol loading, background, and event logic from UnifiedGridPreview
  const [containerDimensions, setContainerDimensions] = useState({
    width: 0,
    height: 0
  });
  const [computedScaleState, setComputedScale] = useState(1);

  // Pre-load common symbols to memory for faster access
  useEffect(() => {
    const symbolsToPreload = [
      '/assets/symbols/wild.png',
      '/assets/symbols/scatter.png',
      '/assets/symbols/mid_1.png',
      '/assets/symbols/mid_2.png',
      '/assets/symbols/high_1.png',
      '/assets/symbols/high_2.png'
    ];
    
    symbolsToPreload.forEach(src => {
      const img = new Image();
      img.src = src;
    });
  }, []);

  // Listen for loading state changes
  useEffect(() => {
    const handleLoadingStateChange = (e: Event) => {
      const detail = (e as CustomEvent).detail;
      if (detail?.loading !== undefined) {
        console.log(`Professional Grid: Setting symbolsLoading to ${detail.loading} from source: ${detail.source || 'unknown'}`);
        setSymbolsLoading(detail.loading);
        setIsLoading(detail.loading);
        
        if (!detail.loading) {
          setTimeout(() => {
            setIsLoading(false);
          }, 50);
        }
      }
    };
    
    window.addEventListener('symbolLoadingStateChange', handleLoadingStateChange);
    
    return () => {
      window.removeEventListener('symbolLoadingStateChange', handleLoadingStateChange);
    };
  }, []);

  // Update local orientation state when props change
  useEffect(() => {
    setCurrentOrientation(orientation);
  }, [orientation]);

  // Log when showCellBackgrounds changes for debugging
  useEffect(() => {
    console.log(`Professional Grid: showCellBackgrounds changed to ${showCellBackgrounds}`);
  }, [showCellBackgrounds]);

  // Update dimensions on resize
  useEffect(() => {
    const updateDimensions = () => {
      if (containerRef.current) {
        setContainerDimensions({
          width: containerRef.current.clientWidth,
          height: containerRef.current.clientHeight
        });
      }
    };
    
    updateDimensions();
    
    const resizeObserver = new ResizeObserver(updateDimensions);
    if (containerRef.current) {
      resizeObserver.observe(containerRef.current);
    }
    
    return () => {
      resizeObserver.disconnect();
    };
  }, []);

  // Implement scaleToFit functionality
  useEffect(() => {
    if (!scaleToFit || !containerRef.current || !gridContentRef.current) return;
    
    const calculateOptimalScale = () => {
      const container = containerRef.current;
      const gridContent = gridContentRef.current;
      
      if (!container || !gridContent) return 1;
      
      const containerWidth = container.offsetWidth;
      const containerHeight = container.offsetHeight;
      
      const gridWidth = gridContent.scrollWidth;
      const gridHeight = gridContent.scrollHeight;
      
      const paddingFactor = 
        (reels >= 7 || rows >= 5) ? 0.85 :
        (reels >= 5 || rows >= 4) ? 0.88 :
        0.92;
      
      const maxContainerWidth = containerWidth * paddingFactor;
      const maxContainerHeight = containerHeight * paddingFactor;
      
      const scaleByWidth = maxContainerWidth / gridWidth;
      const scaleByHeight = maxContainerHeight / gridHeight;
      
      const baseScale = Math.min(scaleByWidth, scaleByHeight);
      
      const specificGridModifier = 
        (reels === 3 && rows === 3) ? 0.9 :
        (reels === 9 || rows >= 7) ? 0.9 :
        (reels >= 7 || rows >= 5) ? 0.92 :
        1.0;
      
      const optimalScale = baseScale * gridScaleModifier * specificGridModifier;
      
      console.debug(`Professional Grid ${reels}x${rows}: baseScale=${baseScale.toFixed(2)}, modifier=${gridScaleModifier}, specificMod=${specificGridModifier}, final=${optimalScale.toFixed(2)}`);
      
      return optimalScale;
    };
    
    const handleResize = () => {
      const scale = calculateOptimalScale();
      setComputedScale(scale);
    };
    
    handleResize();
    
    window.addEventListener('resize', handleResize);
    
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, [scaleToFit, reels, rows, gridScaleModifier]);

  // Update grid attributes when props change
  useEffect(() => {
    if (gridRef.current) {
      gridRef.current.setAttribute('data-reels', reels.toString());
      gridRef.current.setAttribute('data-rows', rows.toString());
      gridRef.current.setAttribute('data-orientation', currentOrientation);
      
      const header = document.getElementById('grid-preview-header');
      if (header) {
        header.textContent = `${reels}×${rows} grid - ${currentOrientation} mode (Professional)`;
      }
    }
  }, [reels, rows, currentOrientation]);

  // Listen for grid config changed events from Step3_ReelConfiguration
  useEffect(() => {
    const handleGridConfigChange = (e: Event) => {
      const detail = (e as CustomEvent).detail;
      if (detail?.orientation) {
        setCurrentOrientation(detail.orientation);
      }
    };
    
    window.addEventListener('gridConfigChanged', handleGridConfigChange);
    
    return () => {
      window.removeEventListener('gridConfigChanged', handleGridConfigChange);
    };
  }, []);

  // Listen for background changed events from Step6_BackgroundCreator
  useEffect(() => {
    const handleBackgroundChanged = (e: Event) => {
      const detail = (e as CustomEvent).detail;
      console.log('Professional Grid received backgroundChanged event:', detail);
      
      if (detail?.backgroundPath) {
        const { config } = useGameStore.getState();
        useGameStore.getState().updateConfig({
          background: {
            ...config.background,
            backgroundImage: detail.backgroundPath,
            type: detail.type || 'static'
          },
          backgroundImage: detail.backgroundPath
        });
        
        console.log('Professional Grid updated background path in store:', detail.backgroundPath);
        
        setTimeout(() => {
          document.dispatchEvent(new Event('refreshGridContent'));
        }, 50);
      }
    };
    
    window.addEventListener('backgroundChanged', handleBackgroundChanged);
    
    return () => {
      window.removeEventListener('backgroundChanged', handleBackgroundChanged);
    };
  }, []);

  // Listen for grid refresh events from refresh button
  useEffect(() => {
    const handleGridRefresh = () => {
      console.log('Professional Grid refresh triggered');
      console.log('Current backgroundPath:', backgroundPath);
      
      try {
        const { theme } = useGameStore.getState();
        if (theme?.generated?.symbols && theme.generated.symbols.length > 0) {
          console.log('Professional Grid: Directly loading symbols from store on grid refresh:', 
            theme.generated.symbols.length);
          
          setSymbolImages(theme.generated.symbols);
          prevSymbolsRef.current = [...theme.generated.symbols];
        } else {
          window.dispatchEvent(new CustomEvent('requestSymbols'));
          console.log('Professional Grid: Requesting symbols due to grid refresh (store empty)');
        }
      } catch (error) {
        console.error('Professional Grid error getting symbols from store:', error);
        window.dispatchEvent(new CustomEvent('requestSymbols'));
      }
      
      setIsMobile(prevState => {
        setTimeout(() => setIsMobile(prevState), 50);
        return !prevState;
      });
      
      console.log(`Professional Grid current symbolImages count: ${symbolImages.length}`);
      
      const refreshDetailEvent = new CustomEvent('gridContentRefreshed', {
        detail: { timestamp: Date.now(), symbolCount: symbolImages.length }
      });
      document.dispatchEvent(refreshDetailEvent);
    };
    
    document.addEventListener('refreshGridContent', handleGridRefresh);
    
    return () => {
      document.removeEventListener('refreshGridContent', handleGridRefresh);
    };
  }, []);

  // Helper function to get symbol URLs from both array and object formats
  const getSymbolUrls = (symbols: string[] | Record<string, string> | undefined): string[] => {
    if (!symbols) return [];
    if (Array.isArray(symbols)) return symbols;
    return Object.values(symbols);
  };

  // Check the store for symbols when our local symbolImages are empty
  useEffect(() => {
    try {
      const { theme } = useGameStore.getState();
      const symbolUrls = getSymbolUrls(theme?.generated?.symbols);

      if (symbolImages.length === 0 && symbolUrls.length > 0) {
        console.log('Professional Grid: No local symbols, loading from store:',
          symbolUrls.length);

        setSymbolImages(symbolUrls);
        prevSymbolsRef.current = [...symbolUrls];
      }
      
      if (symbolsLoading && symbolImages.length > 0) {
        setTimeout(() => {
          setSymbolsLoading(false);
          setIsLoading(false);
        }, 100);
      }
    } catch (error) {
      console.error('Professional Grid error checking store for symbols:', error);
      setSymbolsLoading(false);
    }
  }, [symbolImages.length, symbolsLoading]);

  // Listen for symbolsChanged events - complete implementation
  useEffect(() => {
    const handleSymbolsChanged = (e: Event) => {
      const detail = (e as CustomEvent).detail;
      console.log('Professional Grid received symbolsChanged event:', detail);
      
      requestInProgressRef.current = false;
      
      if (detail?.loading !== undefined) {
        setSymbolsLoading(detail.loading);
      }
      
      if (detail?.symbols && Array.isArray(detail.symbols)) {
        const symbolTypes = {
          all: detail.symbols.length,
          base64: detail.symbols.filter((s: string) => s.startsWith('data:')).length,
          url: detail.symbols.filter((s: string) => !s.startsWith('data:')).length,
          medium: detail.symbols.filter((s: string) => s.includes('medium') || s.includes('mid')).length
        };
        console.log('Professional Grid symbol analysis:', symbolTypes);
        
        const isGenerating = detail.source === 'generateSymbol' || detail.forceUpdate === true;
        const isSameSymbols = 
          !isGenerating && 
          prevSymbolsRef.current.length === detail.symbols.length && 
          JSON.stringify(prevSymbolsRef.current) === JSON.stringify(detail.symbols);
          
        if (isSameSymbols) {
          console.log('Professional Grid: Skipping symbol update - received identical symbols');
          setSymbolsLoading(false);
          setIsLoading(false);
          return;
        }
        
        const hasWild = detail.symbols.some((s: string) => s.includes('wild'));
        const hasScatter = detail.symbols.some((s: string) => s.includes('scatter'));
        const hasMedium = symbolTypes.medium > 0;
        
        prevSymbolsRef.current = [...detail.symbols];
        
        setSymbolImages(detail.symbols);
        
        setSymbolsLoading(false);
        setIsLoading(false);
        console.log('Professional Grid updated with symbols:', detail.symbols.length, 'Previous count:', symbolImages.length);
        
        setTimeout(() => {
          const refreshDetailEvent = new CustomEvent('gridContentRefreshed', {
            detail: { timestamp: Date.now() }
          });
          document.dispatchEvent(refreshDetailEvent);
        }, 50);
      } else {
        setSymbolsLoading(false);
        setIsLoading(false);
        console.log('Professional Grid: No symbols in event, ensuring loading state is false');
      }
    };
    
    const handleRequestSymbols = () => {
      if (requestInProgressRef.current) {
        console.log('Professional Grid: Ignoring duplicate symbol request - request already in progress');
        return;
      }
      
      requestInProgressRef.current = true;
      
      if (symbolImages.length > 0) {
        console.log('Professional Grid: Already have symbols, no need to request more');
        requestInProgressRef.current = false;
        return;
      }
      
      console.log('Professional Grid: Processing symbol request');
      
      try {
        const { theme } = useGameStore.getState();
        if (theme?.generated?.symbols && theme.generated.symbols.length > 0) {
          console.log('Professional Grid: Found symbols in store during request:', theme.generated.symbols.length);
          
          setSymbolImages(theme.generated.symbols);
          prevSymbolsRef.current = [...theme.generated.symbols];
          setSymbolsLoading(false);
          setIsLoading(false);
          requestInProgressRef.current = false;
        } else {
          requestInProgressRef.current = false;
        }
      } catch (error) {
        console.error('Professional Grid error processing symbol request:', error);
        requestInProgressRef.current = false;
      }
    };
    
    window.addEventListener('symbolsChanged', handleSymbolsChanged);
    window.addEventListener('requestSymbols', handleRequestSymbols);
    
    if (symbolImages.length === 0 && !requestInProgressRef.current) {
      window.dispatchEvent(new CustomEvent('requestSymbols'));
    }
    
    const checkStoreForSymbols = () => {
      try {
        const { theme } = useGameStore.getState();
        const symbolUrls = getSymbolUrls(theme?.generated?.symbols);

        if (symbolUrls.length > 0) {
          console.log('Professional Grid: Checking store for symbols on mount:', symbolUrls.length);

          setSymbolImages(symbolUrls);
          prevSymbolsRef.current = [...symbolUrls];
          setSymbolsLoading(false);
          setIsLoading(false);
        }
      } catch (error) {
        console.error('Professional Grid error checking store for symbols:', error);
      }
    };
    
    checkStoreForSymbols();
    
    const timeout = setTimeout(() => {
      if (symbolImages.length === 0 && !requestInProgressRef.current) {
        window.dispatchEvent(new CustomEvent('requestSymbols'));
      }
    }, 500);
    
    return () => {
      window.removeEventListener('symbolsChanged', handleSymbolsChanged);
      window.removeEventListener('requestSymbols', handleRequestSymbols);
      clearTimeout(timeout);
    };
  }, [symbolImages.length]);
  
  // Simple Professional GSAP spin handler - Focus on working basics
  const handleProfessionalSpin = () => {
    if (isSpinning) return;
    
    console.log('🎰 Starting SIMPLE Professional GSAP spin...');
    setIsSpinning(true);
    setStoppedReels(new Array(reels).fill(false));
    
    if (onSpin) {
      onSpin();
    }
    
    // Generate final results
    const availableSymbols = symbolImages.length > 0 ? symbolImages : [
      '/assets/symbols/wild.png',
      '/assets/symbols/scatter.png',
      '/assets/symbols/high_1.png',
      '/assets/symbols/high_2.png',
      '/assets/symbols/high_3.png',
      '/assets/symbols/mid_1.png',
      '/assets/symbols/mid_2.png',
      '/assets/symbols/low_1.png',
      '/assets/symbols/low_2.png',
      '/assets/symbols/low_3.png'
    ];
    
    const newResult: string[][] = [];
    for (let reel = 0; reel < reels; reel++) {
      const reelSymbols: string[] = [];
      for (let row = 0; row < rows; row++) {
        const randomIndex = Math.floor(Math.random() * availableSymbols.length);
        reelSymbols.push(availableSymbols[randomIndex]);
      }
      newResult.push(reelSymbols);
    }
    
    // Simple approach: animate each reel independently
    for (let reel = 0; reel < reels; reel++) {
      const reelDelay = reel * 200; // 200ms stagger
      
      setTimeout(() => {
        console.log(`🎯 STARTING SIMPLE REEL ${reel}`);
        
        // Get cells for this reel
        const reelCells: HTMLElement[] = [];
        for (let row = 0; row < rows; row++) {
          const cell = document.querySelector(`[data-reel="${reel}"][data-row="${row}"]`) as HTMLElement;
          if (cell) {
            reelCells.push(cell);
            const initialTransform = getComputedStyle(cell).transform;
            console.log(`   ✅ Found cell ${reel},${row} - initialTransform='${initialTransform}'`);
          }
        }
        
        if (reelCells.length === 0) {
          console.error(`❌ NO CELLS FOUND FOR REEL ${reel}`);
          return;
        }
        
        const spinDuration = 1.5 + (reel * 0.3); // Progressive timing
        let frameCount = 0;
        const maxFrames = Math.floor(spinDuration * 15); // 15 FPS
        
        console.log(`🔄 REEL ${reel}: ${reelCells.length} cells, ${spinDuration}s duration, ${maxFrames} frames`);
        
        // Simple spinning animation with detailed debugging
        console.log(`🎬 ANIMATION START - REEL ${reel}: frameCount=0, maxFrames=${maxFrames}`);
        
        const spinTimer = setInterval(() => {
          frameCount++;
          const progress = frameCount / maxFrames;
          
          // Detailed progress logging
          if (frameCount % 5 === 0) {
            console.log(`📊 REEL ${reel} FRAME ${frameCount}: progress=${(progress*100).toFixed(1)}%`);
          }
          
          if (progress >= 1) {
            // STOP: Set final symbols
            clearInterval(spinTimer);
            console.log(`🛑 REEL ${reel} STOPPED - Setting final symbols after ${frameCount} frames`);
            
            reelCells.forEach((cell, rowIndex) => {
              const finalSymbol = newResult[reel][rowIndex];
              
              // Debug stopping phase
              const preStopTransform = getComputedStyle(cell).transform;
              console.log(`🔄 STOPPING REEL ${reel} CELL ${rowIndex}: preStopTransform='${preStopTransform}'`);
              
              // Force symbol display - try all methods
              const innerDiv = cell.querySelector('div') as HTMLElement;
              if (innerDiv) {
                innerDiv.style.backgroundImage = `url(${finalSymbol})`;
                innerDiv.style.backgroundSize = 'contain';
                innerDiv.style.backgroundPosition = 'center';
                innerDiv.style.backgroundRepeat = 'no-repeat';
                console.log(`   📌 Set ${finalSymbol.split('/').pop()?.substring(0, 10)}... on inner div`);
              }
              
              // Also try cell itself
              cell.style.backgroundImage = `url(${finalSymbol})`;
              cell.style.backgroundSize = 'contain';
              cell.style.backgroundPosition = 'center';
              cell.style.backgroundRepeat = 'no-repeat';
              
              // Reset transforms - clean stop with no lingering effects
              gsap.set(cell, { 
                y: 0, 
                filter: 'none', 
                opacity: 1,
                transform: 'none' // Clear any residual transforms
              });
              
              // Debug post-stop state
              const postStopTransform = getComputedStyle(cell).transform;
              console.log(`✅ STOPPED REEL ${reel} CELL ${rowIndex}: postStopTransform='${postStopTransform}'`);
            });
            
            // Mark reel as stopped
            setStoppedReels(prev => {
              const updated = [...prev];
              updated[reel] = true;
              
              // Check if all done
              if (updated.every(stopped => stopped)) {
                setTimeout(() => {
                  console.log('🎰 ALL REELS COMPLETE!');
                  setIsSpinning(false);
                  setSpinResult(newResult);
                }, 200);
              }
              
              return updated;
            });
            
            return;
          }
          
          // SPINNING: Proper reel animation - change symbols and move entire reel down
          reelCells.forEach((cell, rowIndex) => {
            // Change symbol randomly during spinning
            if (Math.random() < 0.6) {
              const randomSymbol = availableSymbols[Math.floor(Math.random() * availableSymbols.length)];
              
              // Update symbol display
              const innerDiv = cell.querySelector('div') as HTMLElement;
              if (innerDiv) {
                innerDiv.style.backgroundImage = `url(${randomSymbol})`;
                innerDiv.style.backgroundSize = 'contain';
                innerDiv.style.backgroundPosition = 'center';
                innerDiv.style.backgroundRepeat = 'no-repeat';
              }
              
              cell.style.backgroundImage = `url(${randomSymbol})`;
              cell.style.backgroundSize = 'contain';
              cell.style.backgroundPosition = 'center';
              cell.style.backgroundRepeat = 'no-repeat';
            }
          });
          
          // Apply proper reel motion - smooth downward movement for entire reel
          const baseSpeed = progress < 0.8 ? 6 : (6 * (1.0 - (progress - 0.8) / 0.2)); // Slow down at end
          const totalMovement = frameCount * baseSpeed; // Accumulating downward movement
          const reelOffset = totalMovement % 40; // Cycle every 40px for smooth loop
          
          // Debug motion calculations
          if (frameCount % 10 === 0) {
            console.log(`🔄 REEL ${reel} MOTION: frame=${frameCount}, progress=${(progress*100).toFixed(1)}%, baseSpeed=${baseSpeed.toFixed(2)}, totalMovement=${totalMovement.toFixed(2)}, reelOffset=${reelOffset.toFixed(2)}`);
          }
          
          reelCells.forEach((cell, rowIndex) => {
            // Get current position before transform
            const currentTransform = getComputedStyle(cell).transform;
            
            // Smooth downward reel motion - positive y moves DOWN
            gsap.set(cell, {
              y: reelOffset, // Positive values = downward motion
              filter: 'none', // NO BLUR
              opacity: 1 // Full opacity
            });
            
            // Debug transform application
            if (frameCount % 10 === 0 && rowIndex === 0) {
              const newTransform = getComputedStyle(cell).transform;
              console.log(`🎯 REEL ${reel} CELL ${rowIndex}: currentTransform='${currentTransform}' → newTransform='${newTransform}' (y=${reelOffset})`);
            }
          });
          
          // Log progress
          if (frameCount % 15 === 0) {
            console.log(`   🔄 REEL ${reel}: ${Math.round(progress * 100)}% complete`);
          }
          
        }, 66); // ~15 FPS
        
      }, reelDelay);
    }
  };
  
  // Copy exact cell rendering from UnifiedGridPreview with GSAP integration
  const renderGridCells = () => {
    if (reels <= 0 || rows <= 0 || reels > 9 || rows > 9) {
      return null;
    }
    
    const cells = [];
    
    for (let row = 0; row < rows; row++) {
      for (let reel = 0; reel < reels; reel++) {
        const isWinningCell = payMechanism === 'betlines' ? 
          (row === Math.floor(rows/2) && reel <= Math.min(4, reels-1)) : 
          (payMechanism === 'cluster' && 
           row > 0 && row < rows-1 && reel > 0 && reel < reels-1 && 
           Math.abs(row - Math.floor(rows/2)) + Math.abs(reel - Math.floor(reels/2)) <= 1);
        
        const isReelSpinning = isSpinning && (stoppedReels.length === 0 || !stoppedReels[reel]);
        
        cells.push(
          <motion.div
            key={`cell-${reel}-${row}`}
            className={`relative flex items-center justify-center ${
              isSmallGrid ? 'border-2 border-gray-700/80' : 'border border-gray-700/70'
            } hover:border-blue-400/80 rounded-md overflow-hidden ${
              isWinningCell ? 'border-yellow-400 ring-1 ring-yellow-400/50' : ''
            } ${isReelSpinning ? 'reel-spinning' : ''}`}
            custom={(row * reels) + reel}
            initial="hidden"
            animate={isLoading ? "loading" : (animate ? (isWinningCell ? ["visible", "animate"] : "visible") : "hidden")}
            whileHover="hover"
            style={{
              backgroundColor: showCellBackgrounds ? '#0a1428' : 'transparent',
              boxShadow: isWinningCell ? 
                "inset 0 0 10px rgba(255,215,0,0.5), 0 0 15px rgba(255,215,0,0.4)" : 
                showCellBackgrounds ? (isSmallGrid ? "inset 0 0 3px rgba(0,0,0,0.3)" : "inset 0 0 5px rgba(0,0,0,0.5)") : "none",
              width: `${symbolSize}px`,
              height: `${symbolSize}px`,
              maxWidth: `${symbolSize}px`,
              maxHeight: `${symbolSize}px`,
            }}
            data-reel={reel}
            data-row={row}
            data-winning={isWinningCell ? 'true' : 'false'}
          >
            {isWinningCell && (
              <div className="absolute inset-0 bg-yellow-400/10 z-10 animate-pulse"></div>
            )}
            
            <div 
              className={`flex items-center justify-center w-full h-full ${
                isWinningCell ? 'text-yellow-400' : 'text-gray-300'
              }`}
            >
              {symbolsLoading || isLoading ? (
                <div className="w-full h-full flex items-center justify-center animate-pulse">
                  <div 
                    className={`w-2/3 h-2/3 rounded-md shadow-inner ${
                      (row === 1 && rows === 3) || row === Math.floor(rows/2) ? 
                        'bg-purple-700/40 border border-purple-500/30' : 
                        'bg-gray-700/50'
                    }`}
                  ></div>
                </div>
              ) : (
                symbolImages.length > 0 ? (
                  (() => {
                    let imageSrc: string;
                    
                    // Use spin result if available and not spinning, otherwise use normal distribution
                    if (spinResult.length > 0 && spinResult[reel] && spinResult[reel][row] && !isReelSpinning) {
                      imageSrc = spinResult[reel][row];
                    } else {
                      const symbolIndex = (row * reels + reel) % symbolImages.length;
                      imageSrc = symbolImages[symbolIndex];
                    }
                    
                    if (imageSrc) {
                      return (
                        <div 
                          className="w-full h-full flex items-center justify-center"
                          style={{
                            backgroundImage: `url(${imageSrc})`,
                            backgroundSize: 'contain',
                            backgroundPosition: 'center',
                            backgroundRepeat: 'no-repeat'
                          }}
                        >
                          {(imageSrc.startsWith('data:') || imageSrc.includes('/saved-images/')) && (
                            <img 
                              src={imageSrc} 
                              alt={`Symbol ${(row * reels + reel) % symbolImages.length}`}
                              className="max-w-full max-h-full object-contain"
                              loading="eager"
                              onLoad={(e) => {
                                (e.target as HTMLImageElement).classList.add('loaded-success');
                                console.log(`Professional Grid: Successfully loaded symbol image: ${imageSrc.substring(0, 30)}...`);
                                
                                const parent = (e.target as HTMLImageElement).parentElement;
                                if (parent) {
                                  parent.style.opacity = '0.99';
                                  setTimeout(() => {
                                    parent.style.opacity = '1';
                                  }, 10);
                                }
                              }}
                              onError={(e) => {
                                console.error(`Professional Grid: Error loading image: ${imageSrc}`);
                                
                                const isSpecialSymbol = 
                                  imageSrc.includes('wild') || 
                                  imageSrc.includes('scatter');
                                
                                if (isSpecialSymbol) {
                                  if (imageSrc.includes('wild')) {
                                    (e.target as HTMLImageElement).src = '/assets/symbols/wild.png';
                                  } else {
                                    (e.target as HTMLImageElement).src = '/assets/symbols/scatter.png';
                                  }
                                } else {
                                  (e.target as HTMLImageElement).src = '/assets/symbols/placeholder.png';
                                }
                                
                                (e.target as HTMLImageElement).dataset.fallback = 'true';
                                
                                const parent = (e.target as HTMLImageElement).parentElement;
                                if (parent) {
                                  parent.style.opacity = '0.99';
                                  setTimeout(() => {
                                    parent.style.opacity = '1';
                                  }, 10);
                                }
                                
                                setTimeout(() => {
                                  console.log('Professional Grid: Requesting refresh after image error');
                                  document.dispatchEvent(new Event('refreshGridContent'));
                                }, 100);
                              }}
                            />
                          )}
                        </div>
                      );
                    }
                    // Fallback to text if image is not valid
                    return (
                      <div className="font-bold">
                        {(row * reels + reel) % symbolImages.length + 1}
                      </div>
                    );
                  })()
                ) : (
                  // Fallback to text symbols if no images are available - Copy exact logic from UnifiedGridPreview
                  (() => {
                    const symbols = ['10', 'J', 'Q', 'K', 'A', '♦', '♥', '♠', '♣', 'W'];
                    
                    // Special symbols in specific positions
                    if (reel === Math.floor(reels/2) && row === Math.floor(rows/2)) {
                      // Wild in center
                      return (
                        <div 
                          className="text-yellow-400 font-bold"
                          style={{ 
                            fontSize: `${Math.min(fontSize * 1.2, 32)}px`,
                            textShadow: '0 0 5px rgba(255,215,0,0.7)'
                          }}
                        >
                          W
                        </div>
                      );
                    } else if (reel === reels-1 && row === 0) {
                      // Scatter in top right (bonus symbol)
                      return (
                        <div 
                          className="text-blue-400 font-bold"
                          style={{
                            fontSize: `${Math.min(fontSize * 1.2, 32)}px`,
                            textShadow: '0 0 5px rgba(59,130,246,0.7)'
                          }}
                        >
                          ★
                        </div>
                      );
                    } else {
                      // Regular symbols - distributed to make visual sense
                      const baseIndex = (row * reels + reel) % symbols.length;
                      
                      // Determine symbol type based on position
                      let symbolType: 'high' | 'medium' | 'low';
                      let isSpecialMedium = false;
                      
                      // Force medium symbols in certain positions to ensure they're visible
                      if ((reel === 1 && row === 1) || (reel === 2 && row === 1) || 
                          (rows === 3 && row === 1) || // Always use medium symbols for middle row in 3-row grids
                          (rows > 3 && row === Math.floor(rows/2))) { // Middle row for grids with more than 3 rows
                        symbolType = 'medium';
                        isSpecialMedium = true;
                      } else if (row === 0) {
                        symbolType = 'high';
                      } else if (row === 1 || (rows <= 3 && row === rows - 1)) {
                        symbolType = 'medium';
                      } else {
                        symbolType = 'low';
                      }
                      
                      // Card symbols (high value)
                      if (symbolType === 'high') {
                        const symbolIndex = (baseIndex % 5); // High value symbols (10, J, Q, K, A)
                        return (
                          <div 
                            className="font-bold"
                            data-symbol-type="high"
                            style={{
                              fontSize: `${fontSize}px`,
                              textShadow: '0 0 3px rgba(0,0,0,0.5)'
                            }}
                          >
                            {symbols[symbolIndex]}
                          </div>
                        );
                      } 
                      // Medium value symbols (suits)
                      else if (symbolType === 'medium') {
                        // Use special styling for debug positions
                        const symbolIndex = 5 + (baseIndex % 4); // Medium value symbols (suits)
                        return (
                          <div 
                            className={
                              isSpecialMedium ? 
                                (row === 1 || row === Math.floor(rows/2) ? 'text-purple-500 font-bold' : 'text-green-500 font-bold') :
                              symbolIndex === 6 || symbolIndex === 5 ? 'text-red-400 font-bold' : 'text-blue-200 font-bold'
                            }
                            data-symbol-type="medium"
                            style={{
                              fontSize: `${fontSize}px`,
                              textShadow: isSpecialMedium ?
                                (row === 1 || row === Math.floor(rows/2) ? 
                                  '0 0 5px rgba(168,85,247,0.7)' : // Purple glow for middle row
                                  '0 0 5px rgba(16,185,129,0.6)') : // Green glow for other special mediums
                                symbolIndex === 6 || symbolIndex === 5 ? 
                                  '0 0 3px rgba(248,113,113,0.4)' : '0 0 3px rgba(56,189,248,0.4)',
                              border: isSpecialMedium ? 
                                (row === 1 || row === Math.floor(rows/2) ? 
                                  '1px solid rgba(168,85,247,0.6)' : // Purple border for middle row
                                  '1px solid rgba(16,185,129,0.5)') : // Green border for other special mediums
                                'none'
                            }}
                          >
                            {isSpecialMedium ? (row === 1 || row === Math.floor(rows/2) ? '♣' : '♦') : symbols[symbolIndex]}
                          </div>
                        );
                      } 
                      // Numbers/low value
                      else {
                        const symbolIndex = baseIndex % 5; // Low value symbols
                        return (
                          <div
                            className="text-gray-400 font-bold"
                            data-symbol-type="low"
                            style={{
                              fontSize: `${fontSize}px`,
                              textShadow: '0 0 3px rgba(156,163,175,0.3)'
                            }}
                          >
                            {symbols[symbolIndex]}
                          </div>
                        );
                      }
                    }
                  })()
                )
              )}
            </div>
          </motion.div>
        );
      }
    }
    
    return cells;
  };
  
  // Copy exact layout calculations from UnifiedGridPreview
  
  const calculateVerticalOffset = () => {
    const baseOffset = -5;
    
    if (reels === 3 && rows === 3) {
      return baseOffset + 2;
    } else if (reels === 4 && rows === 3) {
      return baseOffset + 1; 
    } else if (reels === 5 && rows === 3) {
      return baseOffset;
    } else if (reels === 5 && rows === 4) {
      return baseOffset - 1;
    } else if (reels >= 6 && rows <= 3) {
      return baseOffset - 1;
    } else if (reels >= 6 && rows >= 4) {
      return baseOffset - 2;
    } else if (reels >= 7 || rows >= 5) {
      return baseOffset - 3;
    }
    
    return verticalOffset !== 0 ? verticalOffset : baseOffset;
  };
  
  const finalVerticalOffset = calculateVerticalOffset();
  
  const gridContainerStyle = scaleToFit ? {
    position: 'absolute' as 'absolute',
    top: `calc(50% + ${finalVerticalOffset}%)`,
    left: '50%',
    transform: `translate(-50%, -50%) scale(${computedScaleState})`,
    transformOrigin: 'center',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    margin: '0 auto'
  } : {
    width: '100%',
    height: '100%',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center'
  };
  
  const containerStyle = {
    aspectRatio: containerAspectRatio,
    backgroundColor: '#0f172a',
    overflow: 'hidden',
    width: '100%',
    height: '100%',
    position: 'relative' as 'relative',
    paddingBottom: '72px',
  };
  
  // Copy exact motion variants from UnifiedGridPreview
  const cellVariants = {
    hidden: { 
      opacity: 0, 
      scale: 0.8,
      rotateY: -90
    },
    visible: { 
      opacity: 1, 
      scale: 1,
      rotateY: 0,
      transition: {
        type: "spring",
        duration: 0.6,
        bounce: 0.3
      }
    },
    loading: {
      opacity: [0.3, 0.8, 0.3],
      scale: [0.95, 1.05, 0.95],
      transition: {
        duration: 1.5,
        repeat: Infinity,
        ease: "easeInOut"
      }
    },
    hover: {
      scale: 1.05,
      transition: { duration: 0.2 }
    }
  };
  
  const winningCellVariants = {
    ...cellVariants,
    animate: {
      scale: [1, 1.1, 1],
      rotateZ: [0, 5, -5, 0],
      transition: {
        duration: 2,
        repeat: Infinity,
        ease: "easeInOut"
      }
    }
  };
  
  // Apply premium shadow styling based on grid size - Copy exact function from UnifiedGridPreview
  const getGridShadowStyle = () => {
    if (isSmallGrid) {
      return 'inset 0 0 40px rgba(0,40,80,0.7), 0 0 25px rgba(0,0,0,0.7), 0 0 1px rgba(59,130,246,0.5)';
    } else if (isMediumGrid) {
      return 'inset 0 0 35px rgba(0,40,80,0.6), 0 0 20px rgba(0,0,0,0.6), 0 0 1px rgba(59,130,246,0.4)';
    } else {
      return 'inset 0 0 30px rgba(0,40,80,0.5), 0 0 15px rgba(0,0,0,0.5), 0 0 1px rgba(59,130,246,0.3)';
    }
  };

  // Render portrait mode virtual phone container - Copy exact from UnifiedGridPreview
  const renderPortraitMode = () => {
    // Calculate the virtual phone container size based on available space
    const containerWidth = containerDimensions.width;
    const containerHeight = containerDimensions.height;
    
    // iPhone aspect ratio
    const phoneRatio = 390 / 844;
    
    // Calculate phone size to fit in container while maintaining aspect ratio
    let phoneWidth, phoneHeight;
    
    if (containerHeight * phoneRatio <= containerWidth) {
      // Height constrained
      phoneHeight = containerHeight * 0.9; // 90% of available height
      phoneWidth = phoneHeight * phoneRatio;
    } else {
      // Width constrained
      phoneWidth = containerWidth * 0.9; // 90% of available width
      phoneHeight = phoneWidth / phoneRatio;
    }
    
    return (
      <>
        {/* Phone frame */}
        <div 
          className="relative rounded-[40px] bg-black shadow-xl overflow-hidden"
          style={{
            width: `${phoneWidth}px`,
            height: `${phoneHeight}px`,
            maxWidth: '100%',
            maxHeight: '100%',
            boxShadow: '0 0 0 2px rgba(255,255,255,0.1), 0 10px 40px rgba(0,0,0,0.5)'
          }}
        >
          {/* Phone notch */}
          <div 
            className="absolute top-0 left-1/2 transform -translate-x-1/2 w-1/4 h-[30px] bg-black z-20 rounded-b-xl"
            style={{ 
              boxShadow: 'inset 0 -2px 8px rgba(255,255,255,0.1)' 
            }}
          >
            <div className="absolute left-1/2 top-[10px] transform -translate-x-1/2 w-[8px] h-[8px] rounded-full bg-gray-500"></div>
          </div>
          
          {/* Phone screen with dark blue game background */}
          <div 
            className="w-full h-full flex flex-col bg-[#051425] pt-[40px] pb-[20px] px-[10px]"
          >
            {/* Game title bar */}
            <div className="h-[40px] flex items-center justify-between px-2 bg-gradient-to-r from-blue-900/80 to-indigo-900/80 rounded-t-lg">
              <div className="text-white font-semibold text-sm">Professional Slot</div>
              <div className="flex items-center space-x-2">
                <div className="w-4 h-4 rounded-full bg-blue-500/80 flex items-center justify-center">
                  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" className="w-3 h-3 text-white fill-current">
                    <path d="M8 3.5a.5.5 0 0 1 .5.5v2a.5.5 0 0 1-.5.5H6a.5.5 0 0 1 0-1h1.5V4a.5.5 0 0 1 .5-.5z"/>
                    <path d="M8 0a8 8 0 1 0 0 16A8 8 0 0 0 8 0zm0 15A7 7 0 1 1 8 1a7 7 0 0 1 0 14z"/>
                  </svg>
                </div>
                <div className="text-white text-xs">$1,000</div>
              </div>
            </div>
            
            {/* Mobile Grid Area */}
            <div 
              className="flex-grow bg-[#041022] flex items-center justify-center p-4"
            >
              {/* Grid Cells Container - Scaled appropriately for mobile */}
              <div 
                className={`slot-grid-container relative rounded-md ${
                  isSmallGrid ? 'border-2' : 'border'
                } border-blue-700 bg-gradient-to-br from-gray-900 to-gray-950 flex items-center justify-center`}
                data-testid="slot-grid-container"
                style={{
                  width: `${85}%`,
                  height: `${60}%`,
                  padding: `${gridPadding}px`,
                  boxShadow: getGridShadowStyle(),
                  position: 'relative', // Needed for frame positioning
                  overflow: 'hidden' // For background and frame overflow control
                }}
                data-grid-size={`${reels}x${rows}`}
                data-orientation={currentOrientation}
                id={id}
              >
                {/* Background image if provided (inside grid container) */}
                {backgroundPath && (
                  <div 
                    className="absolute inset-0 z-0" 
                    style={{
                      backgroundImage: `url(${backgroundPath})`,
                      backgroundSize: 'cover',
                      backgroundPosition: 'center',
                      opacity: 0.9,
                      backgroundRepeat: 'no-repeat',
                      transform: 'scale(1.1)', // Slightly larger to cover the container
                      filter: 'blur(0px)' // Optional subtle blur for depth
                    }}
                  />
                )}
                
                {/* Grid with proper slot machine styling */}
                <div 
                  className="slot-grid flex flex-wrap items-center justify-center relative z-10"
                  style={{
                    display: 'grid',
                    gridTemplateColumns: `repeat(${reels}, ${symbolSize}px)`,
                    gridTemplateRows: `repeat(${rows}, ${symbolSize}px)`,
                    gap: `${gapSize}px`,
                    width: 'auto',
                    height: 'auto',
                  }}
                >
                  {renderGridCells()}
                </div>
                
                {/* Frame overlay if provided */}
                {framePath && (
                  <div 
                    className="absolute inset-0 z-20 pointer-events-none" 
                    style={{
                      backgroundImage: `url(${framePath})`,
                      backgroundSize: 'contain',
                      backgroundPosition: 'center',
                      backgroundRepeat: 'no-repeat',
                      transform: `translate(${framePosition.x}px, ${framePosition.y}px) scale(${frameScale/100}) scaleX(${frameStretch.x/100}) scaleY(${frameStretch.y/100})`
                    }}
                  />
                )}
              </div>
            </div>
            
            {/* Mobile Header - Just visual, actual UI is rendered below */}
            <div className="h-[50px] bg-gradient-to-r from-gray-900 to-gray-800 rounded-b-lg"></div>
          </div>
        </div>
        
        {/* Fallback message if no cells render */}
        {(!reels || !rows) && (
          <div className="absolute inset-0 flex items-center justify-center text-white bg-black/50">
            <p>No valid grid dimensions ({reels}×{rows})</p>
          </div>
        )}
      </>
    );
  };
  
  const renderLandscapeMode = () => {
    // Copy exact logic from UnifiedGridPreview
    const getScaleFactor = () => {
      if (reels <= 5) return 1.0;
      return Math.max(0.8, 5 / reels);
    };
    
    const scaleFactor = getScaleFactor();
    
    return (
      <>
        {showDebugLabel && (
          <div className="absolute top-2 left-2 z-10 px-2 py-1 bg-gray-800/80 text-white text-xs rounded">
            {debugLabel}
          </div>
        )}
        
        <div 
          className={`slot-grid-container relative rounded-md ${
            isSmallGrid ? 'border-2' : isMediumGrid ? 'border-2' : 'border'
          } border-blue-700 bg-transparent flex items-center justify-center`}
          data-testid="slot-grid-container"
          style={{
            width: `${gridWidthPct}%`,
            height: `${gridHeightPct - 10}%`,
            padding: `${gridPadding}px`,
            boxShadow: getGridShadowStyle(),
            transition: 'all 0.3s ease-in-out',
            marginBottom: '20px',
            position: 'relative',
            overflow: 'hidden'
          }}
          data-grid-size={`${reels}x${rows}`}
          data-orientation={currentOrientation}
          id={id}
        >
          <div 
            className="scaled-grid-container relative z-10"
            style={{
              transform: `scale(${scaleFactor})`,
              transformOrigin: 'center center'
            }}
          >
            <div 
              className="slot-grid flex flex-wrap items-center justify-center"
              style={{
                display: 'grid',
                gridTemplateColumns: `repeat(${reels}, ${symbolSize}px)`,
                gridTemplateRows: `repeat(${rows}, ${symbolSize}px)`,
                gap: `${gapSize}px`,
                width: 'auto',
                height: 'auto',
              }}
            >
              {renderGridCells()}
            </div>
          </div>
          
          {framePath && (
            <div 
              className="absolute inset-0 z-20 pointer-events-none" 
              style={{
                backgroundImage: `url(${framePath})`,
                backgroundSize: 'contain',
                backgroundPosition: 'center',
                backgroundRepeat: 'no-repeat',
                transform: `translate(${framePosition.x}px, ${framePosition.y}px) scale(${frameScale/100}) scaleX(${frameStretch.x/100}) scaleY(${frameStretch.y/100})`
              }}
            />
          )}
        </div>
        
        {(!reels || !rows) && (
          <div className="absolute inset-0 flex items-center justify-center text-white bg-black/50">
            <p>No valid grid dimensions ({reels}×{rows})</p>
          </div>
        )}
      </>
    );
  };
  
  return (
    <div 
      ref={containerRef}
      className={`slot-frame relative flex flex-col items-center justify-center ${className}`}
      style={containerStyle}
      data-orientation={currentOrientation}
      data-reels={reels}
      data-rows={rows}
      data-scale-to-fit={scaleToFit ? 'true' : 'false'}
      data-is-mobile={isMobile.toString()}
      data-is-landscape={isLandscape.toString()}
      id="professional-unified-grid-preview"
    >
      {backgroundPath && (
        <div 
          className="absolute inset-0 z-0" 
          style={{
            backgroundImage: `url(${backgroundPath})`,
            backgroundSize: 'cover',
            backgroundPosition: 'center',
            opacity: 0.9,
            backgroundRepeat: 'no-repeat'
          }}
        />
      )}
      
      <div 
        ref={gridRef}
        style={gridContainerStyle}
        className="flex-grow flex items-center justify-center pb-0"
      >
        <div ref={gridContentRef}>
          {currentOrientation === 'portrait' 
            ? renderPortraitMode() 
            : renderLandscapeMode()
          }
        </div>
      </div>
      
      {showUnifiedUI && (
        <>
          {isMobile ? (
            <>
              {isLandscape ? (
                <div className="absolute inset-0 z-30 pointer-events-auto">
                  <MobileLandscapeUI
                    onSpin={handleProfessionalSpin}
                    onAutoplayToggle={() => console.log('Autoplay toggled')}
                    onMenuToggle={() => console.log('Menu clicked')}
                    onSoundToggle={() => console.log('Sound toggled')}
                    onBetChange={() => console.log('Bet changed')}
                    balance={balance}
                    bet={bet}
                    win={win}
                    className="h-full w-full"
                  />
                </div>
              ) : (
                <div className="absolute inset-0 z-30 pointer-events-auto">
                  <MobilePortraitUI
                    onSpin={handleProfessionalSpin}
                    onAutoplayToggle={() => console.log('Autoplay toggled')}
                    onMenuToggle={() => console.log('Menu clicked')}
                    onSoundToggle={() => console.log('Sound toggled')}
                    onBetChange={() => console.log('Bet changed')}
                    balance={balance}
                    bet={bet}
                    win={win}
                    className="h-full w-full"
                  />
                </div>
              )}
            </>
          ) : (
            <>
              {!isMobile && orientation === 'landscape' && (
                <div className="absolute bottom-0 left-0 right-0 w-full z-20">
                  <SlotGameUI 
                    onSpin={handleProfessionalSpin}
                    onAutoplayToggle={() => {}}
                    onMaxBet={() => {}}
                    balance={balance}
                    bet={bet}
                    win={win}
                    spinButtonSvg={spinButtonSvg}
                    spinButtonImageUrl={spinButtonImageUrl}
                    customButtons={config.uiElements}
                    className="shadow-lg border-t border-gray-700/50 h-[72px]"
                  />
                </div>
              )}
            </>
          )}
        </>
      )}
    </div>
  );
};

export default ProfessionalUnifiedGridPreview;