import { SymbolConfig } from "../../types/EnhancedAnimationLabStep4";


interface SymbolCarouselItemProps {
  symbol: SymbolConfig;
  isSelected: boolean;
  onClick: () => void;
  isGenerating?: boolean;
  progress?: number;
}

const SymbolCarouselItem: React.FC<SymbolCarouselItemProps> = ({ 
  symbol, 
  isSelected, 
  onClick, 
  isGenerating = false, 
  progress = 0 
}) => {
  const getFrameStyle = () => {
    if (isSelected) {
      return 'ring-2 ring-red-500 ring-offset-2';
    }
    
    switch (symbol.rarity) {
      // case 'legendary': // Wild symbols
      //   return 'border-2 border-yellow-400 shadow-md shadow-yellow-400/20';
      // case 'epic': // Scatter symbols  
      //   return 'border-2 border-red-400 shadow-md shadow-red-400/20';
      // case 'rare': // High-value symbols
      //   return 'border-2 border-blue-400 shadow-md shadow-blue-400/20';
      default: // Common symbols
        return 'border border-gray-300';
    }
  };
  
  const getRarityIcon = () => {
    switch (symbol.rarity) {
      case 'legendary':
        return '👑';
      case 'epic':
        return '⚡';
      case 'rare':
        return '⭐';
      default:
        return '⭕';
    }
  };

  const getSymbolTypeIcon = () => {
    switch (symbol.gameSymbolType) {
      case 'wild':
        return '🃏';
        case 'wild 2':
        return '🃏';
      case 'scatter':
        return '💫';
      case 'high 1':
        return '💎';
      case 'high 2':      
        return '💎';
      case 'high 3':      
        return '💎';
      case 'high 4':      
        return '💎';
      case 'medium 1':
        return '🔸';
        case 'medium 2':
        return '🔸';
        case 'medium 3':
        return '🔸';
        case 'medium 4':
        return '🔸';
      case 'low 1':
        return '🔹';
        case 'low 2':
        return '🔹';
        case 'low 3':
        return '🔹';
        case 'low 4':
        return '🔹';
      default:
        return '🎯';
    }
  };

  const getSymbolTypeColor = () => {
    switch (symbol.gameSymbolType) {
      case 'wild':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
        case 'wild 2':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'scatter':
        return 'bg-purple-100 text-purple-800 border-purple-200';
      case 'high 1':
        return 'bg-red-100 text-red-800 border-red-200';
        case 'high 2':
        return 'bg-red-100 text-red-800 border-red-200';
        case 'high 3':
        return 'bg-red-100 text-red-800 border-red-200';
        case 'high 4':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'medium 1':
        return 'bg-blue-100 text-blue-800 border-blue-200';
        case 'medium 2':
        return 'bg-blue-100 text-blue-800 border-blue-200';
        case 'medium 3':
        return 'bg-blue-100 text-blue-800 border-blue-200';
        case 'medium 4':
        return 'bg-blue-100 text-blue-800 border-blue-200';
        case 'low 1':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'low 2':
        return 'bg-green-100 text-green-800 border-green-200';
        case 'low 3':
        return 'bg-green-100 text-green-800 border-green-200';
        case 'low 4':
        return 'bg-green-100 text-green-800 border-green-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };
  
  return (
    <div
      onClick={onClick}
      className={`
        w-[120px] h-[120px] bg-white rounded-lg cursor-pointer
        transition-all duration-200 hover:shadow-md 
        ${getFrameStyle()}
      `}
    >
      {/* Symbol Image Area */}
      <div className="relative w-full h-[90px] bg-gray-50 rounded-t-lg flex items-center justify-center overflow-hidden">
        {symbol.imageUrl ? (
          <img
            src={symbol.imageUrl}
            alt={symbol.name}
            className="w-full h-full object-contain p-3"
          />
        ) : isGenerating ? (
          <div className="flex flex-col items-center p-3">
            <div className="w-8 h-8 border-2 border-red-500 border-t-transparent rounded-full animate-spin mb-2"></div>
            <div className="w-full bg-gray-200 rounded-full h-1.5">
              <div 
                className="h-1.5 bg-red-500 rounded-full transition-all duration-300"
                style={{ width: `${progress}%` }}
              />
            </div>
          </div>
        ) : (
          <div className="flex flex-col items-center text-gray-400">
            <div className="text-2xl mb-1">🖼️</div>
            <span className="text-xs">Empty</span>
          </div>
        )}
        
        {/* Completion Badge */}
        {symbol.imageUrl && (
          <div className="absolute top-2 right-2 w-4 h-4 bg-green-500 rounded-full border-2 border-white flex items-center justify-center">
            <span className="text-white text-xs">✓</span>
          </div>
        )}
        
        {/* Symbol Type Badge */}
        <div className="absolute top-2 left-2 flex items-center gap-1">
          <span className="text-sm">{getSymbolTypeIcon()}</span>
          <span className="text-xs">{getRarityIcon()}</span>
        </div>
      </div>

      {/* Symbol Info */}
      <div className="h-[30px] px-2 py-1 flex items-center justify-between">
        <span className="text-xs font-medium text-gray-700 truncate">
          {symbol.name}
        </span>
        <div className={`text-xs px-1 py-0.5 rounded border text-center min-w-[40px] ${getSymbolTypeColor()}`}>
          {symbol.gameSymbolType?.toUpperCase() || 'SYM'}
        </div>
      </div>
    </div>
  );
};

export default SymbolCarouselItem