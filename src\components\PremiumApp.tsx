import React, { useState, useEffect, lazy, Suspense, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import { useGameStore } from '../store';
import PremiumLayout from './layout/PremiumLayout';
import { GameCanvasContainer } from './canvas';
import EnhancedGameCrafterDashboard from './EnhancedGameCrafterDashboard';
import ConfigModal from './ConfigModal';
import { Loader, Sparkles } from 'lucide-react';

// Import step components - keep using the existing components to preserve functionality
import ThemeExplorer from './visual-journey/ThemeExplorer';
import Step2_GameTypeSelector from './visual-journey/steps-working/Step2_GameTypeSelector';
import Step3_ReelConfiguration from './visual-journey/steps-working/Step3_ReelConfiguration';
import Step4_SymbolCreation from './visual-journey/steps/Step4_SymbolCreation';
import Step5_SymbolAnimation from './visual-journey/steps-working/Step6_SymbolAnimation';
import Step6_GameAssets from './visual-journey/steps-working/Step4_GameAssets';
import { Step7_AnimationStudioIntegration } from './visual-journey/steps/Step7_AnimationStudioIntegration';
import AudioComponent from './visual-journey/steps-working/Step11_AudioComponent';
import Step8_WinAnimationWorkshop from './visual-journey/steps-working/Step8_WinAnimationWorkshop';
import Step8_SplashPreloader from './visual-journey/steps-working/Step10_SplashPreloader';
import Step9_LoadingExperience from './visual-journey/steps-working/Step9_LoadingExperience';
import Step8_BonusFeatures from './visual-journey/steps/Step8_BonusFeatures';
import Step9_MathLab from './visual-journey/steps-working/Step13_MathLab';
import Step10_DeepSimulation from './visual-journey/steps-working/Step14_DeepSimulation';
import Step11_MarketCompliance from './visual-journey/steps/Step11_MarketCompliance';
import Step12_APIExport from './visual-journey/steps/Step12_APIExport';

// Custom Theme Explorer with no color customization
import EnhancedThemeExplorer from './visual-journey/steps-working/Step1_EnhancedThemeExplorer';

// Lazy load the magic box page
const MagicBoxPage = lazy(() => import('./MagicBoxPage'));
const GameCanvasDemo = lazy(() => import('./GameCanvasDemo'));

// Single source of truth for all steps
const SLOT_STEPS = [
  {
    id: 'theme-selection',
    title: 'Theme Selection',
    description: 'Choose the perfect visual theme',
    component: EnhancedThemeExplorer || ThemeExplorer, // Use enhanced version if available
    showCanvas: false // No canvas needed for theme selection
  },
  {
    id: 'game-type',
    title: 'Game Type',
    description: 'Select your game mechanics',
    component: Step2_GameTypeSelector,
    showCanvas: false // No canvas needed for game type selection
  },
  {
    id: 'reel-config',
    title: 'Grid Layout',
    description: 'Configure your game grid',
    component: Step3_ReelConfiguration,
    showCanvas: true // Start showing canvas for grid layout
  },
  {
    id: 'game-assets',
    title: 'Game Assets',
    description: 'Customize backgrounds, frames, and UI elements',
    component: Step6_GameAssets,
    showCanvas: true
  },
  {
    id: 'symbol-creation',
    title: 'Symbol Creation',
    description: 'Create expressive game symbols',
    component: Step4_SymbolCreation,
    showCanvas: true // Show premium slot preview on right side
  },
  {
    id: 'symbol-animation',
    title: 'Symbol Animation',
    description: 'Animate symbols with professional effects',
    component: Step5_SymbolAnimation,
    showCanvas: true // Step 5 handles its own internal PIXI preview
  },
  {
    id: 'animation-studio',
    title: 'Animation & Masking Studio',
    description: 'Configure real-time animations, masking, and visual effects',
    component: Step7_AnimationStudioIntegration,
    showCanvas: true
  },
  {
    id: 'win-animations',
    title: 'Win Animations',
    description: 'Create exciting win celebrations',
    component: Step8_WinAnimationWorkshop,
    showCanvas: true
  },
  {
    id: 'loading-experience',
    title: 'Loading Experience',
    description: 'Design professional loading screens and progress indicators',
    component: Step9_LoadingExperience,
    showCanvas: false
  },
  {
    id: 'splash-screen',
    title: 'Game Splash & Branding',
    description: 'Design game introduction screen and branding elements',
    component: Step8_SplashPreloader,
    showCanvas: false
  },
  {
    id: 'audio',
    title: 'Audio & Experience',
    description: 'Add sounds and music',
    component: AudioComponent,
    showCanvas: true
  },
  {
    id: 'bonus-features',
    title: 'Bonus Features',
    description: 'Design special gameplay mechanics',
    component: Step8_BonusFeatures,
    showCanvas: true
  },
  {
    id: 'math-model',
    title: 'Math Model',
    description: 'Configure RTP and volatility',
    component: Step9_MathLab,
    showCanvas: true
  },
  {
    id: 'simulation',
    title: 'Game Simulation',
    description: 'Test your game with thousands of spins',
    component: Step10_DeepSimulation,
    showCanvas: true
  },
  {
    id: 'compliance',
    title: 'Market Compliance',
    description: 'Ensure your game meets regulatory requirements for different markets.',
    component: Step11_MarketCompliance,
    showCanvas: false // No canvas needed for compliance
  },
  {
    id: 'api-export',
    title: 'API Export',
    description: 'Export your game for deployment',
    component: Step12_APIExport,
    showCanvas: false // No canvas needed for export
  }
];

// Keep scratch steps for compatibility
const SCRATCH_STEPS = [
  {
    id: 'theme-selection',
    title: 'Theme Selection',
    description: 'Choose your card theme',
    component: EnhancedThemeExplorer || ThemeExplorer,
    showCanvas: false
  },
  // Additional scratch card steps would go here
];

/**
 * Premium App Component
 * 
 * A complete redesign of the main App component with premium UI/UX patterns
 * and unified navigation across all game types.
 */
const PremiumApp: React.FC = () => {
  const navigate = useNavigate();
  
  // UI States
  const [showConfig, setShowConfig] = useState(false);
  const [showIntro, setShowIntro] = useState(true);
  
  // Add a manual forceUpdate mechanism to handle stuck UI
  const [, setForceUpdateCounter] = useState(0);
  const forceUpdate = useCallback(() => setForceUpdateCounter(prev => prev + 1), []);
  
  // Game States from store
  const { 
    gameType, 
    setGameType, 
    currentStep, 
    setStep, 
    hasUnsavedChanges, 
    saveProgress,
    config
  } = useGameStore();
  
  // Auto-hide intro animation on mount
  useEffect(() => {
    // Auto-hide intro animation after 2.5 seconds
    const timer = setTimeout(() => {
      setShowIntro(false);
    }, 2500);
    
    return () => clearTimeout(timer);
  }, []);
  
  // Get appropriate steps based on game type - memoize to prevent recalculations
  const steps = React.useMemo(() => {
    switch (gameType) {
      case 'scratch':
        return SCRATCH_STEPS;
      case 'slots':
      case 'visual_journey':
      default:
        return SLOT_STEPS;
    }
  }, [gameType]);
  
  // Track step changes with stable dependencies
  useEffect(() => {
    // Save to localStorage as backup for emergency recovery
    try {
      localStorage.setItem('slotai_last_step', currentStep.toString());
    } catch (e) {
      // Ignore localStorage errors
    }
  }, [currentStep]);
  
  // Navigation handlers - memoized to prevent recreating on every render
  const handlePrevStep = useCallback(() => {
    if (currentStep > 0) {
      setStep(currentStep - 1);
    }
  }, [currentStep, setStep]);
  
  const handleNextStep = useCallback(async () => {
    // Special handling for Step 0 -> Step 1 transition (Theme Selection to Game Type)
    if (currentStep === 0) {
      // Check if a theme has been selected
      if (!config?.theme?.selectedThemeId) {
        // Show an alert to the user
        alert('Please select a theme before continuing to the next step.');
        return;
      }
      
      // Check if a game name has been set
      if (!config.gameId || !config.displayName) {
        alert('Please enter a game name before continuing to the next step.');
        return;
      }
      
      // Ensure we save progress before proceeding
      saveProgress();
      
      // Double-check theme is still selected after save
      const configAfterSave = useGameStore.getState().config;
      if (!configAfterSave?.theme?.selectedThemeId) {
        alert('Theme selection was lost. Please select a theme again.');
        return;
      }
      
      try {
        // Create folder structure for the game
        // Dynamically import to avoid loading until needed
        const { createFoldersForCurrentGame } = await import('../utils/folderCreator');
        
        console.log('Creating folders for game:', config.gameId);
        const success = await createFoldersForCurrentGame();
        
        if (success) {
          console.log('Folders created successfully for game:', config.gameId);
          
          // Show a success toast
          const toast = document.createElement('div');
          toast.style.position = 'fixed';
          toast.style.bottom = '20px';
          toast.style.right = '20px';
          toast.style.backgroundColor = 'rgba(34, 197, 94, 0.9)'; // Green
          toast.style.color = 'white';
          toast.style.padding = '12px 20px';
          toast.style.borderRadius = '8px';
          toast.style.boxShadow = '0 4px 6px rgba(0, 0, 0, 0.1)';
          toast.style.zIndex = '9999';
          toast.style.maxWidth = '80%';
          toast.style.transition = 'all 0.3s ease';
          toast.style.transform = 'translateY(100px)';
          toast.innerHTML = `
            <div style="display: flex; align-items: center;">
              <div style="margin-right: 12px;">✓</div>
              <div>
                <div style="font-weight: bold; margin-bottom: 4px;">Game Folder Created</div>
                <div style="font-size: 14px;">Folder structure for "${config.displayName}" has been created successfully.</div>
              </div>
            </div>
          `;
          
          document.body.appendChild(toast);
          
          // Animate in
          setTimeout(() => {
            toast.style.transform = 'translateY(0)';
          }, 50);
          
          // Remove after delay
          setTimeout(() => {
            toast.style.opacity = '0';
            toast.style.transform = 'translateY(100px)';
            
            setTimeout(() => {
              document.body.removeChild(toast);
            }, 300);
          }, 4000);
        } else {
          console.warn('Failed to create folders for game:', config.gameId);
          // Still proceed to next step, just with a warning
        }
      } catch (error) {
        console.error('Error creating game folders:', error);
        // Still proceed to next step despite the error
      }
      
      // Proceed to next step
      setStep(currentStep + 1);
    } else {
      // Normal transition for other steps
      if (currentStep < steps.length - 1) {
        // Save progress before transitioning
        saveProgress();
        // Navigate to next step
        setStep(currentStep + 1);
      }
    }
  }, [currentStep, config, saveProgress, setStep, steps.length]);
  
  // Toggle preview - now switches InteractiveGameCanvas to test mode
  const togglePreview = useCallback(() => {
    // Instead of fullscreen, we'll trigger test mode in the canvas
    // Dispatch a custom event that the InteractiveGameCanvas can listen to
    window.dispatchEvent(new CustomEvent('toggleTestMode'));
  }, []);
  
  // If no game type selected, show dashboard
  if (!gameType) {
    return (
      <>
        {/* Intro animation */}
        <AnimatePresence>
          {showIntro && (
            <motion.div 
              className="fixed inset-0 bg-gray-900 z-50 flex items-center justify-center"
              initial={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              transition={{ duration: 0.8 }}
            >
              <motion.div
                initial={{ scale: 0.8, opacity: 0 }}
                animate={{ 
                  scale: [0.8, 1.1, 1],
                  opacity: [0, 1, 1]
                }}
                exit={{ 
                  scale: 0.9,
                  opacity: 0
                }}
                transition={{ 
                  duration: 2.5,
                  times: [0, 0.6, 1]
                }}
                className="text-center"
              >
                <motion.div
                  animate={{
                    y: [0, -15, 0],
                    rotateZ: [0, 5, 0, -5, 0],
                    scale: [1, 1.1, 1]
                  }}
                  transition={{
                    duration: 3,
                    repeat: Infinity,
                    repeatType: "reverse"
                  }}
                >
                  <Sparkles className="w-24 h-24 text-red-500 mx-auto" />
                </motion.div>
                <motion.h1
                  className="text-4xl font-bold text-white mt-6 tracking-tight"
                  animate={{
                    letterSpacing: ["0.05em", "0.03em", "0.05em"]
                  }}
                  transition={{
                    duration: 3,
                    repeat: Infinity,
                    repeatType: "reverse"
                  }}
                >
                  SlotAI Game Crafter
                </motion.h1>
                <motion.p
                  className="text-xl text-blue-300 mt-3"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ delay: 0.5, duration: 1 }}
                >
                  Create amazing slot games with AI
                </motion.p>
              </motion.div>
            </motion.div>
          )}
        </AnimatePresence>
      
        <div className={showIntro ? 'opacity-0' : 'opacity-100 transition-opacity duration-500'}>
          <EnhancedGameCrafterDashboard 
            setGameType={setGameType} 
            setStep={setStep} 
            setShowConfig={setShowConfig}
          />
        </div>
        
        {showConfig && (
          <ConfigModal 
            isOpen={showConfig}
            onClose={() => setShowConfig(false)}
          />
        )}
      </>
    );
  }
  
  // Get current step data - using null check to avoid errors
  const currentStepData = steps[currentStep];
  
  // Ensure the component is fetched correctly with fallback
  const CurrentStepComponent = currentStepData?.component || React.Fragment;
  
  // Create a memoized step component renderer that will refresh when step changes
  const StepComponentWithKey = React.useMemo(() => {
    // Return a component that renders the current step component with a unique key
    return function StepRenderer() {
      return (
        <div key={`step-container-${currentStep}`} className="w-full h-full">
          {currentStepData ? (
            <CurrentStepComponent key={`step-${currentStep}`} />
          ) : (
            <div className="p-8 text-center text-red-600 border-2 border-red-200 rounded-lg">
              <h3 className="text-xl font-bold mb-2">Missing Component</h3>
              <p>No component found for step {currentStep}.</p>
              <div className="mt-4 text-sm text-gray-500">
                Try refreshing the page or returning to the dashboard.
              </div>
            </div>
          )}
        </div>
      );
    };
  }, [currentStep, currentStepData, CurrentStepComponent]);
  
  // Conditionally show canvas based on the current step
  const shouldShowCanvas = currentStepData?.showCanvas ?? false;
  
  // Get appropriate canvas elements based on the current configuration
  const getCanvasElements = useCallback(() => {
    const elements = [];
    
    // Add background if we have a theme
    if (config?.theme?.selectedThemeId) {
      // Add game background element
      elements.push({
        id: 'bg-1',
        type: 'background',
        layer: 'background',
        x: 0,
        y: 0,
        width: 1080,
        height: 1920,
        rotation: 0,
        scale: 1,
        opacity: 1,
        zIndex: 0,
        visible: true,
        selected: false,
        data: {
          src: `/themes/${config.theme.selectedThemeId}.png`,
          themeId: config.theme.selectedThemeId,
          themeName: config.theme.mainTheme || 'Unknown Theme',
          colors: config.theme.colors || {},
          color: '#222222'
        }
      });
    }
    
    // For step 4 (symbols) and beyond, add placeholder symbols if needed
    if (currentStep >= 3 && !elements.some(el => el.type === 'symbol')) {
      // Add placeholder symbols
      ['wild', 'scatter', 'high_1', 'mid_1', 'low_1'].forEach((symbolType, index) => {
        elements.push({
          id: `symbol-${index + 1}`,
          type: 'symbol',
          layer: 'symbols',
          x: 100 + (index * 150),
          y: 400,
          width: 120,
          height: 120,
          rotation: 0,
          scale: 1,
          opacity: 1,
          zIndex: 10 + index,
          visible: true,
          selected: false,
          data: {
            symbolType,
            src: `/assets/symbols/${symbolType}.png`,
            name: symbolType.charAt(0).toUpperCase() + symbolType.slice(1).replace('_', ' '),
            value: symbolType.startsWith('high') ? 5 : 
                  symbolType.startsWith('mid') ? 3 : 
                  symbolType.startsWith('low') ? 1 : 0
          }
        });
      });
    }
    
    return elements;
  }, [config?.theme?.selectedThemeId, currentStep]);
  
  // Main premium app rendering
  return (
    <>
      <PremiumLayout
        currentStep={currentStep}
        totalSteps={steps.length}
        stepTitle={currentStepData?.title || 'Game Creation'}
        stepDescription={currentStepData?.description || 'Building your game'}
        onPrevStep={handlePrevStep}
        onNextStep={handleNextStep}
        onSave={saveProgress}
        onPreview={togglePreview}
        showCanvas={shouldShowCanvas}
        hasUnsavedChanges={hasUnsavedChanges}
        gameId={config?.gameId || ''}
        gameName={config?.displayName || 'New Game'}
      >
        <Suspense fallback={<PremiumLoader message={`Loading ${currentStepData?.title || 'content'}`} />}>
          {/* Render the step component */}
          <StepComponentWithKey />
        </Suspense>
      </PremiumLayout>
      
      {showConfig && (
        <ConfigModal 
          isOpen={showConfig}
          onClose={() => setShowConfig(false)}
        />
      )}
    </>
  );
};

/**
 * Premium Loader Component
 * 
 * Stylish loading indicator with animation
 */
const PremiumLoader: React.FC<{message?: string}> = ({ message = 'Loading' }) => {
  return (
    <div className="fixed inset-0 bg-white/80 dark:bg-gray-900/80 backdrop-blur-sm flex items-center justify-center z-50">
      <div className="text-center">
        <div className="relative w-16 h-16 mx-auto mb-4">
          <motion.div
            className="absolute inset-0 rounded-full border-2 border-transparent border-t-indigo-600 border-r-indigo-600"
            animate={{ rotate: 360 }}
            transition={{ duration: 1.5, repeat: Infinity, ease: "linear" }}
          />
          <motion.div
            className="absolute inset-2 rounded-full border-2 border-transparent border-t-red-500 border-l-red-500"
            animate={{ rotate: -360 }}
            transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
          />
          <Loader className="absolute inset-0 w-16 h-16 text-blue-500 opacity-30" />
        </div>
        <motion.p
          className="text-lg font-medium text-gray-700 dark:text-gray-300"
          animate={{ opacity: [0.6, 1, 0.6] }}
          transition={{ duration: 1.5, repeat: Infinity }}
        >
          {message}
        </motion.p>
      </div>
    </div>
  );
};

export default PremiumApp;